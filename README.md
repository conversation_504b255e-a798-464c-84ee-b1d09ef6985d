# 友文印刷后台管理系统

一个专业的印刷业务后台管理系统，基于 Node.js + SQLite 开发，支持用户管理、订单管理、商品管理和多层级属性管理。

## 🚀 功能特色

### 核心功能模块
- **用户管理** - 管理员账户、权限控制、安全认证
- **订单管理** - 订单查看、状态更新、统计分析、数据导出
- **商品管理** - 商品CRUD、库存管理、分类管理、价格计算
- **属性管理** - 多层级属性结构、组合选择、价格调整

### 技术架构
- **后端**: Node.js + Express
- **数据库**: SQLite3
- **认证**: JWT + bcrypt
- **前端**: 现代化管理界面 (Tailwind CSS)
- **安全**: 输入验证、SQL注入防护、权限控制

### 属性管理设计
- **大类属性**: 如"材质"、"尺寸"、"工艺"、"数量"
- **小类属性**: 如材质下的"铜版纸"、"哑粉纸"、"卡纸"等
- **组合选择**: 支持多属性组合定价
- **价格调整**: 每个属性值可设置价格调整

## 📦 安装和启动

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务器
```bash
npm start
# 或开发模式
npm run dev
```

### 3. 访问管理后台
打开浏览器访问: http://localhost:3000/admin

### 4. 默认登录信息
- **用户名**: admin
- **密码**: admin123456

## 🗄️ 数据库结构

### 用户表 (users)
- 用户认证和权限管理
- 支持多角色 (admin, user)
- 密码加密存储

### 属性分类表 (attribute_categories)
- 大类属性定义
- 支持排序和状态管理

### 属性值表 (attribute_values)
- 小类属性值
- 价格调整设置
- 关联到属性分类

### 商品表 (products)
- 商品基本信息
- 库存管理
- 图片和规格存储

### 商品属性关联表 (product_attributes)
- 商品与属性的多对多关系
- 支持必选/可选设置

### 订单表 (orders)
- 订单基本信息
- 状态和支付状态管理

### 订单商品表 (order_items)
- 订单商品详情
- 属性组合记录

## 🔧 API 接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/change-password` - 修改密码

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户

### 属性管理
- `GET /api/attributes/categories` - 获取属性分类
- `POST /api/attributes/categories` - 创建属性分类
- `GET /api/attributes/values` - 获取属性值
- `POST /api/attributes/values` - 创建属性值
- `GET /api/attributes/tree` - 获取完整属性树

### 商品管理
- `GET /api/products` - 获取商品列表
- `POST /api/products` - 创建商品
- `PUT /api/products/:id` - 更新商品
- `DELETE /api/products/:id` - 删除商品
- `POST /api/products/:id/calculate-price` - 计算属性组合价格

### 订单管理
- `GET /api/orders` - 获取订单列表
- `POST /api/orders` - 创建订单
- `PATCH /api/orders/:id/status` - 更新订单状态
- `GET /api/orders/stats/overview` - 获取订单统计

## 🛡️ 安全特性

### 认证和授权
- JWT令牌认证
- 密码bcrypt加密
- 权限级别控制

### 输入验证
- express-validator数据验证
- SQL注入防护
- XSS攻击防护

### 安全中间件
- helmet安全头设置
- CORS跨域控制
- 请求频率限制

## 🎯 业务特色

### 印刷行业专业化
- **商务名片** - 多种材质工艺选择
- **宣传画册** - 企业形象展示
- **三联单据** - 无碳复写管理
- **定制纸箱** - 包装解决方案

### 属性组合定价
- 基础价格 + 属性调整
- 材质、尺寸、工艺、数量组合
- 实时价格计算
- 灵活定价策略

### 订单管理流程
- 待处理 → 已确认 → 处理中 → 已发货 → 已送达
- 支付状态跟踪
- 订单统计分析

## 📊 系统监控

### 仪表盘统计
- 今日订单数量和金额
- 商品总数统计
- 用户总数统计
- 最近订单展示

### 数据导出
- 订单数据CSV导出
- 自定义时间范围
- 状态筛选导出

## 🔄 开发和部署

### 开发环境
```bash
npm run dev  # 使用nodemon自动重启
```

### 生产环境
```bash
NODE_ENV=production npm start
```

### 环境变量配置
```env
PORT=3000
NODE_ENV=development
JWT_SECRET=your_jwt_secret_key
DB_PATH=./database/youwen_printing.db
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123456
```

## 📝 更新日志

### v1.0.0 (2024-06-15)
- ✅ 完整的后台管理系统
- ✅ 用户认证和权限管理
- ✅ 多层级属性管理
- ✅ 商品管理和库存控制
- ✅ 订单管理和状态跟踪
- ✅ 现代化管理界面
- ✅ 安全防护和数据验证

## 🤝 技术支持

如有问题或建议，请联系友文印刷技术团队。

---

**友文印刷** - 专业印刷解决方案提供商
