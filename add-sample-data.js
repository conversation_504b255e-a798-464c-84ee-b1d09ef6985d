const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');

const db = new sqlite3.Database('./database/youwen_printing.db');

console.log('🔧 开始添加示例数据...');

db.serialize(() => {
    // 首先确保有管理员用户
    const hashedPassword = bcrypt.hashSync('admin123456', 12);
    
    db.run(`INSERT OR IGNORE INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)`, 
        ['admin', '<EMAIL>', hashedPassword, 'admin'], 
        function(err) {
            if (err) {
                console.error('❌ 创建管理员失败:', err);
            } else {
                console.log('✅ 管理员账户确认');
            }
        });

    // 添加属性分类
    const categories = [
        { name: 'material', display_name: '材质', description: '印刷材质选择', sort_order: 1 },
        { name: 'size', display_name: '尺寸', description: '产品尺寸规格', sort_order: 2 },
        { name: 'process', display_name: '工艺', description: '印刷工艺处理', sort_order: 3 },
        { name: 'quantity', display_name: '数量', description: '起订数量', sort_order: 4 }
    ];

    categories.forEach((cat, index) => {
        db.run(`INSERT OR IGNORE INTO attribute_categories (name, display_name, description, sort_order) VALUES (?, ?, ?, ?)`,
            [cat.name, cat.display_name, cat.description, cat.sort_order],
            function(err) {
                if (err) {
                    console.error(`❌ 添加分类 ${cat.display_name} 失败:`, err);
                } else {
                    console.log(`✅ 添加分类: ${cat.display_name}`);
                    
                    // 添加对应的属性值
                    addAttributeValues(this.lastID || index + 1, cat.name);
                }
            });
    });

    // 添加示例商品
    setTimeout(() => {
        addSampleProducts();
    }, 1000);
});

function addAttributeValues(categoryId, categoryName) {
    let values = [];
    
    switch(categoryName) {
        case 'material':
            values = [
                { name: 'coated_paper', display_name: '铜版纸', value: '250g', price_modifier: 0 },
                { name: 'art_paper', display_name: '哑粉纸', value: '300g', price_modifier: 5 },
                { name: 'cardboard', display_name: '卡纸', value: '350g', price_modifier: 10 },
                { name: 'special_paper', display_name: '特种纸', value: '400g', price_modifier: 20 }
            ];
            break;
        case 'size':
            values = [
                { name: 'standard', display_name: '标准尺寸', value: '90x54mm', price_modifier: 0 },
                { name: 'large', display_name: '大尺寸', value: '100x60mm', price_modifier: 8 },
                { name: 'custom', display_name: '自定义', value: '定制', price_modifier: 15 }
            ];
            break;
        case 'process':
            values = [
                { name: 'normal', display_name: '普通印刷', value: '四色印刷', price_modifier: 0 },
                { name: 'lamination', display_name: '覆膜', value: '亮膜/哑膜', price_modifier: 12 },
                { name: 'uv', display_name: 'UV工艺', value: '局部UV', price_modifier: 25 },
                { name: 'emboss', display_name: '烫金', value: '金色/银色', price_modifier: 30 }
            ];
            break;
        case 'quantity':
            values = [
                { name: 'qty_500', display_name: '500张', value: '500', price_modifier: 0 },
                { name: 'qty_1000', display_name: '1000张', value: '1000', price_modifier: -5 },
                { name: 'qty_2000', display_name: '2000张', value: '2000', price_modifier: -12 },
                { name: 'qty_5000', display_name: '5000张', value: '5000', price_modifier: -25 }
            ];
            break;
    }

    values.forEach((val, index) => {
        db.run(`INSERT OR IGNORE INTO attribute_values (category_id, name, display_name, value, price_modifier, sort_order) VALUES (?, ?, ?, ?, ?, ?)`,
            [categoryId, val.name, val.display_name, val.value, val.price_modifier, index + 1],
            function(err) {
                if (err) {
                    console.error(`❌ 添加属性值 ${val.display_name} 失败:`, err);
                } else {
                    console.log(`  ✅ 添加属性值: ${val.display_name}`);
                }
            });
    });
}

function addSampleProducts() {
    const products = [
        {
            name: '商务名片',
            description: '高端商务名片，专业设计，多种材质工艺可选',
            category: '名片类',
            base_price: 50.00,
            stock_quantity: 1000,
            min_quantity: 500,
            unit: '张',
            images: JSON.stringify(['/uploads/business-card.jpg']),
            specifications: JSON.stringify({
                default_size: '90x54mm',
                thickness: '0.35mm',
                colors: '四色印刷'
            })
        },
        {
            name: '宣传画册',
            description: '企业宣传画册，精美印刷，展示企业形象',
            category: '画册类',
            base_price: 120.00,
            stock_quantity: 500,
            min_quantity: 100,
            unit: '本',
            images: JSON.stringify(['/uploads/brochure.jpg']),
            specifications: JSON.stringify({
                pages: '16页起',
                binding: '骑马钉',
                size: 'A4'
            })
        },
        {
            name: '三联单据',
            description: '无碳复写三联单据，便于记录和管理',
            category: '单据类',
            base_price: 80.00,
            stock_quantity: 800,
            min_quantity: 200,
            unit: '本',
            images: JSON.stringify(['/uploads/carbonless-form.jpg']),
            specifications: JSON.stringify({
                layers: '三联',
                paper: '无碳复写纸',
                size: 'A5'
            })
        },
        {
            name: '定制纸箱',
            description: '定制包装纸箱，安全包装，物流保障',
            category: '包装类',
            base_price: 15.00,
            stock_quantity: 2000,
            min_quantity: 100,
            unit: '个',
            images: JSON.stringify(['/uploads/custom-box.jpg']),
            specifications: JSON.stringify({
                material: '瓦楞纸',
                strength: '高强度',
                custom: '可定制尺寸'
            })
        }
    ];

    products.forEach(product => {
        db.run(`INSERT OR IGNORE INTO products (name, description, category, base_price, stock_quantity, min_quantity, unit, images, specifications) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [product.name, product.description, product.category, product.base_price, product.stock_quantity, product.min_quantity, product.unit, product.images, product.specifications],
            function(err) {
                if (err) {
                    console.error(`❌ 添加商品 ${product.name} 失败:`, err);
                } else {
                    console.log(`✅ 添加商品: ${product.name}`);
                }
            });
    });

    console.log('🎉 示例数据添加完成！');
    db.close();
}
