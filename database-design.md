# 印刷品商城数据库设计

## 数据库表结构设计

### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 1  -- 1:正常 0:禁用
);
```

### 2. 商品分类表 (categories)
```sql
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    parent_id INTEGER DEFAULT 0,  -- 支持多级分类
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 商品表 (products)
```sql
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category_id INTEGER,
    base_price DECIMAL(10,2) DEFAULT 0,  -- 基础价格
    images TEXT,  -- JSON格式存储图片路径
    status INTEGER DEFAULT 1,  -- 1:上架 0:下架
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

### 4. 商品属性类型表 (product_attribute_types)
```sql
CREATE TABLE product_attribute_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    name VARCHAR(50) NOT NULL,  -- 如：尺码、颜色、材质
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

### 5. 商品属性值表 (product_attribute_values)
```sql
CREATE TABLE product_attribute_values (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    attribute_type_id INTEGER NOT NULL,
    value VARCHAR(50) NOT NULL,  -- 如：XL、红色、棉质
    price_adjustment DECIMAL(10,2) DEFAULT 0,  -- 价格调整（可为负数）
    stock INTEGER DEFAULT 0,  -- 库存数量
    sku VARCHAR(100),  -- SKU编码
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (attribute_type_id) REFERENCES product_attribute_types(id) ON DELETE CASCADE
);
```

### 6. 商品SKU表 (product_skus)
```sql
CREATE TABLE product_skus (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    sku_code VARCHAR(100) UNIQUE NOT NULL,
    attribute_combination TEXT NOT NULL,  -- JSON格式存储属性组合
    price DECIMAL(10,2) NOT NULL,  -- 最终价格
    stock INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

### 7. 订单表 (orders)
```sql
CREATE TABLE orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status INTEGER DEFAULT 1,  -- 1:待处理 2:处理中 3:已完成 4:已取消
    shipping_address TEXT,
    contact_phone VARCHAR(20),
    contact_name VARCHAR(50),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 8. 订单详情表 (order_items)
```sql
CREATE TABLE order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    sku_id INTEGER,
    product_name VARCHAR(100) NOT NULL,
    sku_info TEXT,  -- JSON格式存储SKU信息
    price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (sku_id) REFERENCES product_skus(id)
);
```

### 9. 管理员表 (admins)
```sql
CREATE TABLE admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role VARCHAR(20) DEFAULT 'admin',  -- admin, super_admin
    last_login DATETIME,
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 核心业务逻辑

### 商品属性价格计算逻辑
1. **基础价格**: 商品表中的 base_price
2. **属性调整**: 每个属性值的 price_adjustment
3. **最终价格**: base_price + sum(selected_attributes.price_adjustment)

### SKU生成逻辑
1. 当商品有多个属性时，系统自动生成所有可能的属性组合
2. 每个组合生成一个唯一的SKU
3. SKU价格 = 基础价格 + 所有选中属性的价格调整

### 库存管理
1. 库存以SKU为单位进行管理
2. 下单时检查对应SKU的库存
3. 支持库存预警功能

## 示例数据

### 商品属性配置示例
```json
// 产品：定制T恤
{
  "product_id": 1,
  "base_price": 29.90,
  "attributes": [
    {
      "type": "尺码",
      "values": [
        {"value": "S", "price_adjustment": 0, "stock": 100},
        {"value": "M", "price_adjustment": 0, "stock": 100},
        {"value": "L", "price_adjustment": 5.00, "stock": 80},
        {"value": "XL", "price_adjustment": 10.00, "stock": 50}
      ]
    },
    {
      "type": "颜色",
      "values": [
        {"value": "白色", "price_adjustment": 0, "stock": 200},
        {"value": "黑色", "price_adjustment": 5.00, "stock": 150},
        {"value": "红色", "price_adjustment": 8.00, "stock": 100}
      ]
    }
  ]
}
```

### SKU组合示例
```
SKU: T001-S-WHITE  价格: 29.90 (29.90 + 0 + 0)
SKU: T001-S-BLACK  价格: 34.90 (29.90 + 0 + 5.00)
SKU: T001-L-RED    价格: 42.90 (29.90 + 5.00 + 8.00)
SKU: T001-XL-BLACK 价格: 44.90 (29.90 + 10.00 + 5.00)
```

## 索引优化建议
```sql
-- 商品查询优化
CREATE INDEX idx_products_category_status ON products(category_id, status);
CREATE INDEX idx_products_status_created ON products(status, created_at);

-- 属性查询优化
CREATE INDEX idx_attr_types_product ON product_attribute_types(product_id);
CREATE INDEX idx_attr_values_type ON product_attribute_values(attribute_type_id);

-- SKU查询优化
CREATE INDEX idx_skus_product ON product_skus(product_id, status);
CREATE INDEX idx_skus_code ON product_skus(sku_code);

-- 订单查询优化
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_orders_status_created ON orders(status, created_at);
CREATE INDEX idx_order_items_order ON order_items(order_id);
```
