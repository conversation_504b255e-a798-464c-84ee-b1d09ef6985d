<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>印刷品定制商城 - 专业印刷精品定制</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #6366f1;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover {
            color: #6366f1;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-outline {
            color: #6366f1;
            border: 2px solid #6366f1;
            background: transparent;
        }
        
        .btn-outline:hover {
            background: #6366f1;
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
        }
        
        /* 主要内容区域 */
        .hero-section {
            min-height: 100vh;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
                        linear-gradient(-45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.02) 75%),
                        linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.02) 75%);
            background-size: 60px 60px;
            background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
            opacity: 0.3;
        }
        
        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }
        
        .hero-content h1 {
            font-size: 4rem;
            font-weight: 300;
            color: white;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            letter-spacing: -0.02em;
        }

        .hero-content .subtitle {
            font-size: 1.4rem;
            color: rgba(255, 255, 255, 0.85);
            margin-bottom: 3rem;
            line-height: 1.5;
            font-weight: 300;
            max-width: 600px;
        }
        
        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .btn-hero {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 12px;
        }
        
        .btn-white {
            background: white;
            color: #1e293b;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .btn-white:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            background: #f8fafc;
        }

        .btn-transparent {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .btn-transparent:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        /* 右侧视觉区域 */
        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .business-visual {
            width: 400px;
            height: 400px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .business-visual::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 4px;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .business-visual::after {
            content: 'PRINTING\ASOLUTIONS';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255,255,255,0.6);
            font-size: 1.2rem;
            font-weight: 300;
            letter-spacing: 2px;
            text-align: center;
            line-height: 1.8;
            white-space: pre;
        }
        
        /* 装饰元素 */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .floating-card {
            position: absolute;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border-radius: 4px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: floatCard 12s ease-in-out infinite;
            font-size: 0.9rem;
            font-weight: 300;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.8);
        }

        @keyframes floatCard {
            0%, 100% { transform: translateY(0px); opacity: 0.6; }
            50% { transform: translateY(-10px); opacity: 0.9; }
        }

        .card-1 {
            top: 15%;
            right: 8%;
            animation-delay: -3s;
        }

        .card-2 {
            bottom: 25%;
            left: 3%;
            animation-delay: -6s;
        }

        .card-3 {
            top: 65%;
            right: 15%;
            animation-delay: -9s;
        }
        
        /* 特色服务区域 */
        .features-section {
            padding: 6rem 0;
            background: #ffffff;
        }
        
        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .section-title p {
            font-size: 1.2rem;
            color: #6b7280;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background: #fafafa;
            padding: 3rem 2rem;
            border-radius: 4px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.04);
            transition: all 0.4s ease;
            border: 1px solid #f0f0f0;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.08);
            background: white;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1e293b, #334155);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 2rem;
            font-weight: 300;
        }
        
        .feature-card h3 {
            font-size: 1.4rem;
            font-weight: 400;
            color: #1e293b;
            margin-bottom: 1.5rem;
            letter-spacing: 0.5px;
        }

        .feature-card p {
            color: #64748b;
            line-height: 1.7;
            font-size: 1rem;
            font-weight: 300;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-container {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .business-visual {
                width: 280px;
                height: 280px;
            }

            .business-visual::after {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="#" class="logo">印刷定制</a>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#products">产品中心</a></li>
                <li><a href="#services">服务介绍</a></li>
                <li><a href="#about">关于我们</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
            <div class="nav-buttons">
                <a href="#" class="btn btn-outline">登录</a>
                <a href="#" class="btn btn-primary">立即下单</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <section class="hero-section" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <h1>专业印刷解决方案<br>企业级定制服务</h1>
                <p class="subtitle">为企业提供全方位印刷服务，从商务名片到包装方案，我们致力于通过专业的印刷技术和优质的服务，助力您的品牌形象提升。</p>
                <div class="hero-buttons">
                    <a href="#" class="btn btn-white btn-hero">立即下单</a>
                    <a href="#" class="btn btn-transparent btn-hero">了解更多</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="business-visual"></div>
                <div class="floating-elements">
                    <div class="floating-card card-1">
                        BUSINESS CARDS
                    </div>
                    <div class="floating-card card-2">
                        CARBONLESS FORMS
                    </div>
                    <div class="floating-card card-3">
                        PACKAGING BOXES
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 特色服务 -->
    <section class="features-section">
        <div class="features-container">
            <div class="section-title">
                <h2>核心优势</h2>
                <p>专业技术 · 品质保证 · 高效服务</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">01</div>
                    <h3>专业设计服务</h3>
                    <p>拥有资深设计团队，提供从概念到成品的全流程设计服务，确保每一件作品都体现专业水准和品牌价值。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">02</div>
                    <h3>高效生产交付</h3>
                    <p>采用先进的生产设备和标准化流程管理，标准产品3-5个工作日交付，紧急订单可提供加急服务。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">03</div>
                    <h3>严格品质控制</h3>
                    <p>建立完善的质量管理体系，从原材料采购到成品出库，每个环节都严格把控，确保产品品质。</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
