<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>印刷品定制商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        /* 导航栏样式 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #6366f1;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            cursor: pointer;
        }
        
        .nav-links a:hover, .nav-links a.active {
            color: #6366f1;
        }
        
        .nav-buttons {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-block;
        }
        
        .btn-outline {
            color: #6366f1;
            border: 2px solid #6366f1;
            background: transparent;
        }
        
        .btn-outline:hover {
            background: #6366f1;
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
        }
        
        /* 页面容器 */
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-section {
            display: none;
        }
        
        .page-section.active {
            display: block;
        }
        
        /* 首页英雄区域 */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 5rem 0;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .hero-content .subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .btn-hero {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 12px;
        }
        
        .btn-white {
            background: white;
            color: #6366f1;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn-white:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .btn-transparent {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .btn-transparent:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        /* 产品网格 */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .product-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .product-image {
            height: 200px;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: #9ca3af;
        }
        
        .product-info {
            padding: 1.5rem;
        }
        
        .product-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .product-price {
            color: #6366f1;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .product-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        /* 特色服务 */
        .features-section {
            padding: 4rem 0;
            background: #f8fafc;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .section-title p {
            font-size: 1.2rem;
            color: #6b7280;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .feature-card p {
            color: #6b7280;
            line-height: 1.6;
        }
        
        /* 管理后台样式 */
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }
        
        .admin-header h2 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            border-left: 4px solid;
        }
        
        .stat-card.primary { border-left-color: #6366f1; }
        .stat-card.success { border-left-color: #10b981; }
        .stat-card.warning { border-left-color: #f59e0b; }
        .stat-card.danger { border-left-color: #ef4444; }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 1rem;
        }
        
        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .table tbody tr:hover {
            background: #f8fafc;
        }
        
        /* 状态标签 */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-cancelled {
            background: #fee2e2;
            color: #991b1b;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .page-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">印刷定制</a>
            <ul class="nav-links">
                <li><a href="#" onclick="showPage('home')" class="active">首页</a></li>
                <li><a href="#" onclick="showPage('products')">产品中心</a></li>
                <li><a href="#" onclick="showPage('admin')">管理后台</a></li>
                <li><a href="#" onclick="showPage('about')">关于我们</a></li>
            </ul>
            <div class="nav-buttons">
                <a href="#" class="btn btn-outline">登录</a>
                <a href="#" class="btn btn-primary">立即下单</a>
            </div>
        </div>
    </nav>

    <!-- 首页 -->
    <div id="home" class="page-section active">
        <section class="hero-section">
            <div class="hero-content">
                <h1>专业印刷<br>精品定制</h1>
                <p class="subtitle">高品质印刷服务，支持个性化定制，快速交付。<br>满足您的各种印刷需求。</p>
                <div class="hero-buttons">
                    <a href="#" onclick="showPage('products')" class="btn btn-white btn-hero">浏览产品</a>
                    <a href="#" onclick="showPage('admin')" class="btn btn-transparent btn-hero">管理后台</a>
                </div>
            </div>
        </section>

        <section class="features-section">
            <div class="page-container">
                <div class="section-title">
                    <h2>我们的优势</h2>
                    <p>专业的印刷技术，优质的服务体验</p>
                </div>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <h3>专业设计</h3>
                        <p>拥有专业的设计团队，为您提供个性化的设计方案，确保每一件作品都独具特色。</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>快速交付</h3>
                        <p>高效的生产流程，标准产品3-5个工作日交付，急单可当天完成。</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">✨</div>
                        <h3>品质保证</h3>
                        <p>严格的质量控制体系，使用优质材料和先进设备，确保每一件产品的品质。</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 产品中心 -->
    <div id="products" class="page-section">
        <div class="page-container">
            <div class="section-title">
                <h2>产品中心</h2>
                <p>专业印刷产品，满足您的各种需求</p>
            </div>
            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image">📄</div>
                    <div class="product-info">
                        <div class="product-title">商务名片</div>
                        <div class="product-price">¥0.15/张 起</div>
                        <div class="product-description">高品质商务名片，多种规格和工艺可选，支持个性化定制。</div>
                        <a href="#" class="btn btn-primary">立即定制</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">📋</div>
                    <div class="product-info">
                        <div class="product-title">三联单据</div>
                        <div class="product-price">¥0.08/联 起</div>
                        <div class="product-description">无碳复写三联单据，适用于发票、收据等各种单据印刷。</div>
                        <a href="#" class="btn btn-primary">立即定制</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">📦</div>
                    <div class="product-info">
                        <div class="product-title">定制纸箱</div>
                        <div class="product-price">¥2.50/个 起</div>
                        <div class="product-description">各种规格包装纸箱，支持LOGO印刷和个性化设计。</div>
                        <a href="#" class="btn btn-primary">立即定制</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">📄</div>
                    <div class="product-info">
                        <div class="product-title">宣传单页</div>
                        <div class="product-price">¥0.12/张 起</div>
                        <div class="product-description">高质量宣传单页印刷，多种纸张和工艺选择。</div>
                        <a href="#" class="btn btn-primary">立即定制</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">🏷️</div>
                    <div class="product-info">
                        <div class="product-title">不干胶标签</div>
                        <div class="product-price">¥0.05/张 起</div>
                        <div class="product-description">各种材质不干胶标签，防水防油，粘性持久。</div>
                        <a href="#" class="btn btn-primary">立即定制</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">📖</div>
                    <div class="product-info">
                        <div class="product-title">画册印刷</div>
                        <div class="product-price">¥8.50/本 起</div>
                        <div class="product-description">企业画册、产品手册等精装印刷，专业装订。</div>
                        <a href="#" class="btn btn-primary">立即定制</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理后台 -->
    <div id="admin" class="page-section">
        <div class="page-container">
            <div class="admin-header">
                <h2>管理后台</h2>
                <p>印刷品商城管理系统</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">📄</div>
                    <div class="stat-number">156</div>
                    <div class="stat-label">总商品数</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">📋</div>
                    <div class="stat-number">23</div>
                    <div class="stat-label">今日订单</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number">8</div>
                    <div class="stat-label">待处理订单</div>
                </div>
                <div class="stat-card danger">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">总用户数</div>
                </div>
            </div>

            <div style="margin-bottom: 2rem;">
                <h3 style="margin-bottom: 1rem;">最近订单</h3>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>客户</th>
                                <th>商品</th>
                                <th>规格</th>
                                <th>数量</th>
                                <th>总价</th>
                                <th>状态</th>
                                <th>下单时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#20240615001</td>
                                <td>张三</td>
                                <td>商务名片</td>
                                <td>90x54mm/双面印刷/覆膜</td>
                                <td>1000张</td>
                                <td>¥230.00</td>
                                <td><span class="status-badge status-pending">待处理</span></td>
                                <td>2024-06-15 10:30</td>
                            </tr>
                            <tr>
                                <td>#20240615002</td>
                                <td>李四</td>
                                <td>三联单据</td>
                                <td>A4/三联/无碳复写</td>
                                <td>500本</td>
                                <td>¥120.00</td>
                                <td><span class="status-badge status-completed">已完成</span></td>
                                <td>2024-06-15 09:15</td>
                            </tr>
                            <tr>
                                <td>#20240615003</td>
                                <td>王五</td>
                                <td>定制纸箱</td>
                                <td>30x20x15cm/五层瓦楞</td>
                                <td>100个</td>
                                <td>¥350.00</td>
                                <td><span class="status-badge status-cancelled">已取消</span></td>
                                <td>2024-06-15 08:45</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <button class="btn btn-primary" style="padding: 1rem; text-align: center;">
                    <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">➕</div>
                    添加新商品
                </button>
                <button class="btn btn-outline" style="padding: 1rem; text-align: center;">
                    <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">📦</div>
                    订单管理
                </button>
                <button class="btn btn-outline" style="padding: 1rem; text-align: center;">
                    <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">👤</div>
                    用户管理
                </button>
                <button class="btn btn-outline" style="padding: 1rem; text-align: center;">
                    <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">📊</div>
                    数据统计
                </button>
            </div>
        </div>
    </div>

    <!-- 关于我们 -->
    <div id="about" class="page-section">
        <div class="page-container">
            <div class="section-title">
                <h2>关于我们</h2>
                <p>专业印刷服务提供商</p>
            </div>
            <div style="background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); text-align: center;">
                <div style="font-size: 4rem; margin-bottom: 2rem;">🏭</div>
                <h3 style="font-size: 2rem; margin-bottom: 1rem; color: #1f2937;">专业印刷，品质保证</h3>
                <p style="font-size: 1.1rem; color: #6b7280; line-height: 1.8; max-width: 800px; margin: 0 auto;">
                    我们是一家专业的印刷服务提供商，拥有多年的行业经验和先进的印刷设备。
                    专注于名片、三联纸、纸箱等各类印刷品的定制服务，为客户提供高品质、快速交付的印刷解决方案。
                    我们始终坚持以客户需求为中心，不断提升服务质量，致力于成为您最信赖的印刷合作伙伴。
                </p>
                <div style="margin-top: 2rem;">
                    <a href="#" onclick="showPage('products')" class="btn btn-primary btn-hero">查看我们的产品</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-section');
            pages.forEach(page => page.classList.remove('active'));

            // 显示指定页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // 更新导航链接状态
            const navLinks = document.querySelectorAll('.nav-links a');
            navLinks.forEach(link => link.classList.remove('active'));

            const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // 平滑滚动到顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 页面切换时滚动到顶部
        const originalShowPage = showPage;
        showPage = function(pageId) {
            originalShowPage(pageId);
            scrollToTop();
        };
    </script>
</body>
</html>
