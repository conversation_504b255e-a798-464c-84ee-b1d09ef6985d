<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>印刷品商城 - 原型设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: #2c3e50;
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #3498db;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 3fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .sidebar {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .content-area {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-section {
            display: none;
        }
        
        .page-section.active {
            display: block;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 100%;
            height: 200px;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
        }
        
        .product-info {
            padding: 1rem;
        }
        
        .product-title {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .product-price {
            color: #e74c3c;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }
        
        .attribute-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }
        
        .attribute-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .sub-attribute {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 4px;
        }
        
        .sub-attribute input {
            flex: 1;
        }
        
        .price-input {
            width: 120px;
        }
        
        .tab-nav {
            display: flex;
            border-bottom: 2px solid #ecf0f1;
            margin-bottom: 2rem;
        }
        
        .tab-btn {
            background: none;
            border: none;
            padding: 1rem 2rem;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-btn.active {
            border-bottom-color: #3498db;
            color: #3498db;
            font-weight: bold;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">印刷品商城</div>
            <ul class="nav-links">
                <li><a href="#" onclick="showPage('home')">首页</a></li>
                <li><a href="#" onclick="showPage('products')">商品管理</a></li>
                <li><a href="#" onclick="showPage('orders')">订单管理</a></li>
                <li><a href="#" onclick="showPage('users')">用户管理</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <div class="main-content">
            <aside class="sidebar">
                <h3>管理菜单</h3>
                <ul style="list-style: none; margin-top: 1rem;">
                    <li style="margin-bottom: 0.5rem;"><a href="#" onclick="showPage('home')" class="btn" style="width: 100%; text-align: center;">仪表盘</a></li>
                    <li style="margin-bottom: 0.5rem;"><a href="#" onclick="showPage('products')" class="btn" style="width: 100%; text-align: center;">商品管理</a></li>
                    <li style="margin-bottom: 0.5rem;"><a href="#" onclick="showPage('add-product')" class="btn btn-success" style="width: 100%; text-align: center;">添加商品</a></li>
                    <li style="margin-bottom: 0.5rem;"><a href="#" onclick="showPage('orders')" class="btn" style="width: 100%; text-align: center;">订单管理</a></li>
                    <li style="margin-bottom: 0.5rem;"><a href="#" onclick="showPage('users')" class="btn" style="width: 100%; text-align: center;">用户管理</a></li>
                </ul>
            </aside>

            <main class="content-area">
                <!-- 首页/仪表盘 -->
                <div id="home" class="page-section active">
                    <h2>仪表盘</h2>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem;">
                        <div style="background: #3498db; color: white; padding: 2rem; border-radius: 8px; text-align: center;">
                            <h3>总商品数</h3>
                            <p style="font-size: 2rem; font-weight: bold;">156</p>
                        </div>
                        <div style="background: #27ae60; color: white; padding: 2rem; border-radius: 8px; text-align: center;">
                            <h3>今日订单</h3>
                            <p style="font-size: 2rem; font-weight: bold;">23</p>
                        </div>
                        <div style="background: #e74c3c; color: white; padding: 2rem; border-radius: 8px; text-align: center;">
                            <h3>待处理订单</h3>
                            <p style="font-size: 2rem; font-weight: bold;">8</p>
                        </div>
                        <div style="background: #f39c12; color: white; padding: 2rem; border-radius: 8px; text-align: center;">
                            <h3>总用户数</h3>
                            <p style="font-size: 2rem; font-weight: bold;">1,234</p>
                        </div>
                    </div>
                </div>

                <!-- 商品管理 -->
                <div id="products" class="page-section">
                    <h2>商品管理</h2>
                    <div class="product-grid">
                        <div class="product-card">
                            <div class="product-image">商品图片</div>
                            <div class="product-info">
                                <div class="product-title">定制T恤</div>
                                <div class="product-price">¥29.90 - ¥89.90</div>
                                <div style="margin-top: 1rem;">
                                    <button class="btn" style="margin-right: 0.5rem;">编辑</button>
                                    <button class="btn btn-danger">删除</button>
                                </div>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-image">商品图片</div>
                            <div class="product-info">
                                <div class="product-title">定制马克杯</div>
                                <div class="product-price">¥19.90 - ¥39.90</div>
                                <div style="margin-top: 1rem;">
                                    <button class="btn" style="margin-right: 0.5rem;">编辑</button>
                                    <button class="btn btn-danger">删除</button>
                                </div>
                            </div>
                        </div>
                        <div class="product-card">
                            <div class="product-image">商品图片</div>
                            <div class="product-info">
                                <div class="product-title">定制帆布袋</div>
                                <div class="product-price">¥15.90 - ¥25.90</div>
                                <div style="margin-top: 1rem;">
                                    <button class="btn" style="margin-right: 0.5rem;">编辑</button>
                                    <button class="btn btn-danger">删除</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 添加商品 -->
                <div id="add-product" class="page-section">
                    <h2>添加商品</h2>
                    <form>
                        <div class="form-group">
                            <label>商品名称</label>
                            <input type="text" placeholder="请输入商品名称">
                        </div>

                        <div class="form-group">
                            <label>商品描述</label>
                            <textarea rows="4" placeholder="请输入商品描述"></textarea>
                        </div>

                        <div class="form-group">
                            <label>商品分类</label>
                            <select>
                                <option>请选择分类</option>
                                <option>服装类</option>
                                <option>杯具类</option>
                                <option>包袋类</option>
                                <option>文具类</option>
                            </select>
                        </div>

                        <h3 style="margin: 2rem 0 1rem 0;">商品属性配置</h3>

                        <!-- 尺码属性示例 -->
                        <div class="attribute-section">
                            <div class="attribute-header">
                                <h4>尺码</h4>
                                <button type="button" class="btn btn-success" onclick="addSubAttribute('size')">添加尺码</button>
                            </div>
                            <div id="size-attributes">
                                <div class="sub-attribute">
                                    <input type="text" placeholder="属性名称" value="S">
                                    <input type="number" placeholder="价格" class="price-input" value="29.90">
                                    <input type="number" placeholder="库存" class="price-input" value="100">
                                    <button type="button" class="btn btn-danger">删除</button>
                                </div>
                                <div class="sub-attribute">
                                    <input type="text" placeholder="属性名称" value="M">
                                    <input type="number" placeholder="价格" class="price-input" value="29.90">
                                    <input type="number" placeholder="库存" class="price-input" value="100">
                                    <button type="button" class="btn btn-danger">删除</button>
                                </div>
                                <div class="sub-attribute">
                                    <input type="text" placeholder="属性名称" value="L">
                                    <input type="number" placeholder="价格" class="price-input" value="35.90">
                                    <input type="number" placeholder="库存" class="price-input" value="80">
                                    <button type="button" class="btn btn-danger">删除</button>
                                </div>
                                <div class="sub-attribute">
                                    <input type="text" placeholder="属性名称" value="XL">
                                    <input type="number" placeholder="价格" class="price-input" value="39.90">
                                    <input type="number" placeholder="库存" class="price-input" value="50">
                                    <button type="button" class="btn btn-danger">删除</button>
                                </div>
                            </div>
                        </div>

                        <!-- 颜色属性示例 -->
                        <div class="attribute-section">
                            <div class="attribute-header">
                                <h4>颜色</h4>
                                <button type="button" class="btn btn-success" onclick="addSubAttribute('color')">添加颜色</button>
                            </div>
                            <div id="color-attributes">
                                <div class="sub-attribute">
                                    <input type="text" placeholder="属性名称" value="白色">
                                    <input type="number" placeholder="价格" class="price-input" value="0">
                                    <input type="number" placeholder="库存" class="price-input" value="200">
                                    <button type="button" class="btn btn-danger">删除</button>
                                </div>
                                <div class="sub-attribute">
                                    <input type="text" placeholder="属性名称" value="黑色">
                                    <input type="number" placeholder="价格" class="price-input" value="5.00">
                                    <input type="number" placeholder="库存" class="price-input" value="150">
                                    <button type="button" class="btn btn-danger">删除</button>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 2rem;">
                            <button type="button" class="btn btn-success" onclick="addNewAttribute()">添加新属性类型</button>
                        </div>

                        <div style="margin-top: 2rem;">
                            <button type="submit" class="btn btn-success" style="margin-right: 1rem;">保存商品</button>
                            <button type="button" class="btn" onclick="showPage('products')">取消</button>
                        </div>
                    </form>
                </div>

                <!-- 订单管理 -->
                <div id="orders" class="page-section">
                    <h2>订单管理</h2>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>客户</th>
                                <th>商品</th>
                                <th>规格</th>
                                <th>数量</th>
                                <th>总价</th>
                                <th>状态</th>
                                <th>下单时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#20240615001</td>
                                <td>张三</td>
                                <td>定制T恤</td>
                                <td>XL/黑色</td>
                                <td>2</td>
                                <td>¥89.80</td>
                                <td><span class="status-badge status-pending">待处理</span></td>
                                <td>2024-06-15 10:30</td>
                                <td>
                                    <button class="btn" style="margin-right: 0.5rem;">查看</button>
                                    <button class="btn btn-success">处理</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#20240615002</td>
                                <td>李四</td>
                                <td>定制马克杯</td>
                                <td>标准/白色</td>
                                <td>5</td>
                                <td>¥99.50</td>
                                <td><span class="status-badge status-completed">已完成</span></td>
                                <td>2024-06-15 09:15</td>
                                <td>
                                    <button class="btn">查看</button>
                                </td>
                            </tr>
                            <tr>
                                <td>#20240615003</td>
                                <td>王五</td>
                                <td>定制帆布袋</td>
                                <td>大号/蓝色</td>
                                <td>1</td>
                                <td>¥25.90</td>
                                <td><span class="status-badge status-cancelled">已取消</span></td>
                                <td>2024-06-15 08:45</td>
                                <td>
                                    <button class="btn">查看</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 用户管理 -->
                <div id="users" class="page-section">
                    <h2>用户管理</h2>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>用户ID</th>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>注册时间</th>
                                <th>订单数</th>
                                <th>总消费</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1001</td>
                                <td>张三</td>
                                <td><EMAIL></td>
                                <td>2024-01-15</td>
                                <td>12</td>
                                <td>¥1,280.50</td>
                                <td><span class="status-badge status-completed">正常</span></td>
                                <td>
                                    <button class="btn">查看</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                            <tr>
                                <td>1002</td>
                                <td>李四</td>
                                <td><EMAIL></td>
                                <td>2024-02-20</td>
                                <td>8</td>
                                <td>¥890.30</td>
                                <td><span class="status-badge status-completed">正常</span></td>
                                <td>
                                    <button class="btn">查看</button>
                                    <button class="btn btn-danger">禁用</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-section');
            pages.forEach(page => page.classList.remove('active'));

            // 显示指定页面
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }
        }

        function addSubAttribute(attributeType) {
            const container = document.getElementById(attributeType + '-attributes');
            const newAttribute = document.createElement('div');
            newAttribute.className = 'sub-attribute';
            newAttribute.innerHTML = `
                <input type="text" placeholder="属性名称">
                <input type="number" placeholder="价格" class="price-input">
                <input type="number" placeholder="库存" class="price-input">
                <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">删除</button>
            `;
            container.appendChild(newAttribute);
        }

        function addNewAttribute() {
            const attributeName = prompt('请输入新属性类型名称（如：材质、工艺等）：');
            if (attributeName) {
                const attributeId = 'attr-' + Date.now();
                const newAttributeSection = document.createElement('div');
                newAttributeSection.className = 'attribute-section';
                newAttributeSection.innerHTML = `
                    <div class="attribute-header">
                        <h4>${attributeName}</h4>
                        <button type="button" class="btn btn-success" onclick="addSubAttribute('${attributeId}')">添加${attributeName}</button>
                    </div>
                    <div id="${attributeId}-attributes">
                        <div class="sub-attribute">
                            <input type="text" placeholder="属性名称">
                            <input type="number" placeholder="价格" class="price-input">
                            <input type="number" placeholder="库存" class="price-input">
                            <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">删除</button>
                        </div>
                    </div>
                `;

                // 插入到添加新属性按钮之前
                const addButton = document.querySelector('button[onclick="addNewAttribute()"]').parentElement;
                addButton.parentElement.insertBefore(newAttributeSection, addButton);
            }
        }
    </script>
</body>
</html>
