<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>友文印刷 | 专业印刷解决方案</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #0F172A;
            --primary-light: #1E293B;
            --secondary: #64748B;
            --accent: #3B82F6;
            --surface: #FFFFFF;
            --surface-alt: #F8FAFC;
            --surface-hover: #F1F5F9;
            --border: #E2E8F0;
            --border-light: #F1F5F9;
            --text-primary: #0F172A;
            --text-secondary: #475569;
            --text-muted: #64748B;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', sans-serif;
            line-height: 1.5;
            color: var(--text-primary);
            background: var(--surface);
            font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-light);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-sm);
        }

        .nav-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 72px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.2s ease;
            cursor: pointer;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary);
        }

        .nav-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.625rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
        }

        .btn-ghost {
            color: var(--text-secondary);
            background: transparent;
        }

        .btn-ghost:hover {
            color: var(--primary);
            background: var(--surface-hover);
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Hero Section */
        .hero {
            padding: 9rem 0 6rem;
            background: linear-gradient(135deg, #FAFBFC 0%, #F1F5F9 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60%;
            height: 100%;
            background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
        }

        .hero-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-content h1 {
            font-size: 3.75rem;
            font-weight: 700;
            line-height: 1.1;
            letter-spacing: -0.025em;
            color: var(--primary);
            margin-bottom: 1.5rem;
        }

        .hero-content .subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .hero-metrics {
            display: flex;
            gap: 2rem;
            margin-bottom: 2.5rem;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            line-height: 1;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-lg {
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
        }

        .btn-outline {
            background: white;
            color: var(--primary);
            border: 1px solid var(--border);
        }

        .btn-outline:hover {
            background: var(--surface-hover);
            border-color: var(--primary);
        }

        /* Hero Visual */
        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .visual-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            width: 100%;
            max-width: 400px;
        }

        .visual-card {
            background: white;
            padding: 2rem 1.5rem;
            border-radius: 1rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-light);
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .visual-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .visual-card:nth-child(1) { transform: translateY(-0.5rem); }
        .visual-card:nth-child(2) { transform: translateY(0.5rem); }
        .visual-card:nth-child(3) { transform: translateY(0.5rem); }
        .visual-card:nth-child(4) { transform: translateY(-0.5rem); }

        .visual-icon {
            width: 3rem;
            height: 3rem;
            background: var(--surface-alt);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.25rem;
        }

        .visual-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Page Container */
        .page-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 6rem 2rem 2rem;
        }

        .page-section {
            display: none;
        }

        .page-section.active {
            display: block;
        }

        /* Section Headers */
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 1rem;
            letter-spacing: -0.025em;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 1rem;
            border: 1px solid var(--border-light);
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--border);
        }

        .product-header {
            padding: 2rem;
            background: var(--surface-alt);
            text-align: center;
            border-bottom: 1px solid var(--border-light);
        }

        .product-icon {
            width: 4rem;
            height: 4rem;
            background: var(--primary);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .product-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .product-price {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .product-body {
            padding: 2rem;
        }

        .product-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .product-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .product-features li {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .product-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #10B981;
            font-weight: 600;
        }

        /* Admin Dashboard */
        .admin-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            padding: 3rem 2rem;
            border-radius: 1rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .admin-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            letter-spacing: -0.025em;
        }

        .admin-subtitle {
            opacity: 0.8;
            font-size: 1.125rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid var(--border-light);
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            background: var(--surface-alt);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.25rem;
            color: var(--text-muted);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Table */
        .table-container {
            background: white;
            border-radius: 1rem;
            border: 1px solid var(--border-light);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-light);
        }

        .table th {
            background: var(--surface-alt);
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .table td {
            font-size: 0.875rem;
        }

        .table tbody tr:hover {
            background: var(--surface-alt);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            background: #FEF3C7;
            color: #92400E;
        }

        .status-completed {
            background: #D1FAE5;
            color: #065F46;
        }

        .status-cancelled {
            background: #FEE2E2;
            color: #991B1B;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-container {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .visual-grid {
                max-width: 300px;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="#" class="logo">友文印刷</a>
            <ul class="nav-links">
                <li><a href="#" onclick="showPage('home')" class="active">首页</a></li>
                <li><a href="#" onclick="showPage('products')">产品中心</a></li>
                <li><a href="#" onclick="showPage('admin')">管理后台</a></li>
                <li><a href="#" onclick="showPage('about')">关于我们</a></li>
            </ul>
            <div class="nav-actions">
                <a href="#" class="btn btn-ghost">登录</a>
                <a href="#" class="btn btn-primary">获取报价</a>
            </div>
        </div>
    </nav>

    <!-- Home Page -->
    <div id="home" class="page-section active">
        <section class="hero">
            <div class="hero-container">
                <div class="hero-content">
                    <h1>专业印刷解决方案</h1>
                    <p class="subtitle">以卓越品质提升企业形象，从商务名片到包装方案，友文印刷为您提供全方位的专业印刷服务，助力企业发展。</p>

                    <div class="hero-metrics">
                        <div class="metric">
                            <span class="metric-value">500+</span>
                            <span class="metric-label">企业客户</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">15年</span>
                            <span class="metric-label">行业经验</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">99.8%</span>
                            <span class="metric-label">客户满意度</span>
                        </div>
                    </div>

                    <div class="hero-actions">
                        <a href="#" onclick="showPage('products')" class="btn btn-primary btn-lg">浏览产品</a>
                        <a href="#" onclick="showPage('admin')" class="btn btn-outline btn-lg">管理后台</a>
                    </div>
                </div>

                <div class="hero-visual">
                    <div class="visual-grid">
                        <div class="visual-card">
                            <div class="visual-icon">📄</div>
                            <div class="visual-title">商务名片</div>
                        </div>
                        <div class="visual-card">
                            <div class="visual-icon">📋</div>
                            <div class="visual-title">单据表格</div>
                        </div>
                        <div class="visual-card">
                            <div class="visual-icon">📦</div>
                            <div class="visual-title">包装纸箱</div>
                        </div>
                        <div class="visual-card">
                            <div class="visual-icon">📖</div>
                            <div class="visual-title">企业画册</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Products Page -->
    <div id="products" class="page-section">
        <div class="page-container">
            <div class="section-header">
                <h2 class="section-title">精品产品</h2>
                <p class="section-subtitle">为现代企业量身定制的专业印刷解决方案</p>
            </div>

            <div class="products-grid">
                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📄</div>
                        <div class="product-title">商务名片</div>
                        <div class="product-price">起价 ¥0.15/张</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">高品质商务名片，留下深刻印象。多种纸张和工艺选择，彰显专业形象。</div>
                        <ul class="product-features">
                            <li>标准90×54mm规格</li>
                            <li>300克高档铜版纸</li>
                            <li>双面精美印刷</li>
                            <li>覆膜烫金工艺可选</li>
                            <li>最低起印100张</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">立即定制</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📋</div>
                        <div class="product-title">三联单据</div>
                        <div class="product-price">起价 ¥0.08/联</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">专业无碳复写单据，适用于发票、收据等商务文档，清晰复写，便于存档。</div>
                        <ul class="product-features">
                            <li>A4/A5标准尺寸</li>
                            <li>无碳复写纸材质</li>
                            <li>二联或三联可选</li>
                            <li>连续编号服务</li>
                            <li>最低起印500本</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">立即定制</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📦</div>
                        <div class="product-title">定制纸箱</div>
                        <div class="product-price">起价 ¥2.50/个</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">个性化包装解决方案，保护产品的同时提升品牌形象，专业结构设计。</div>
                        <ul class="product-features">
                            <li>三层或五层瓦楞纸</li>
                            <li>自定义尺寸规格</li>
                            <li>全彩色印刷工艺</li>
                            <li>结构设计支持</li>
                            <li>最低起印50个</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">立即定制</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📖</div>
                        <div class="product-title">企业画册</div>
                        <div class="product-price">起价 ¥8.50/本</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">高品质企业画册和产品目录，专业展示企业形象和产品实力。</div>
                        <ul class="product-features">
                            <li>A4/A5多种规格</li>
                            <li>157克铜版纸内页</li>
                            <li>骑马钉装订工艺</li>
                            <li>设计咨询服务</li>
                            <li>最低起印20本</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">立即定制</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">🏷️</div>
                        <div class="product-title">不干胶标签</div>
                        <div class="product-price">起价 ¥0.05/张</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">耐用的标签贴纸，适用于产品标识和品牌推广，防水防油性能优异。</div>
                        <ul class="product-features">
                            <li>PVC/PET优质材料</li>
                            <li>防水防油性能</li>
                            <li>强力胶粘背胶</li>
                            <li>模切成型工艺</li>
                            <li>最低起印1000张</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">立即定制</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📄</div>
                        <div class="product-title">宣传单页</div>
                        <div class="product-price">起价 ¥0.12/张</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">精美宣传单页和推广材料，有效提升营销效果，传播企业信息。</div>
                        <ul class="product-features">
                            <li>A4/A5/DM单规格</li>
                            <li>128克铜版纸</li>
                            <li>全彩色印刷</li>
                            <li>光膜/哑膜可选</li>
                            <li>最低起印500张</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">立即定制</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin" class="page-section">
        <div class="page-container">
            <div class="admin-header">
                <h2 class="admin-title">管理后台</h2>
                <p class="admin-subtitle">友文印刷业务管理中心</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-number">156</div>
                    <div class="stat-label">产品总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-number">23</div>
                    <div class="stat-label">今日订单</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number">8</div>
                    <div class="stat-label">待处理订单</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">客户总数</div>
                </div>
            </div>

            <div style="margin-bottom: 3rem;">
                <h3 style="margin-bottom: 1.5rem; font-size: 1.5rem; font-weight: 600; color: var(--primary);">最近订单</h3>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>订单编号</th>
                                <th>客户名称</th>
                                <th>产品类型</th>
                                <th>规格要求</th>
                                <th>数量</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>下单时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#YW240615001</td>
                                <td>深圳科技有限公司</td>
                                <td>商务名片</td>
                                <td>90×54mm/双面印刷/覆膜</td>
                                <td>1,000张</td>
                                <td>¥230.00</td>
                                <td><span class="status-badge status-pending">待处理</span></td>
                                <td>2024-06-15 10:30</td>
                            </tr>
                            <tr>
                                <td>#YW240615002</td>
                                <td>广州贸易公司</td>
                                <td>三联单据</td>
                                <td>A4/三联无碳复写</td>
                                <td>500本</td>
                                <td>¥120.00</td>
                                <td><span class="status-badge status-completed">已完成</span></td>
                                <td>2024-06-15 09:15</td>
                            </tr>
                            <tr>
                                <td>#YW240615003</td>
                                <td>东莞制造企业</td>
                                <td>定制纸箱</td>
                                <td>30×20×15cm/五层瓦楞</td>
                                <td>100个</td>
                                <td>¥350.00</td>
                                <td><span class="status-badge status-cancelled">已取消</span></td>
                                <td>2024-06-15 08:45</td>
                            </tr>
                            <tr>
                                <td>#YW240615004</td>
                                <td>佛山电子公司</td>
                                <td>企业画册</td>
                                <td>A4/20页/胶装</td>
                                <td>50本</td>
                                <td>¥425.00</td>
                                <td><span class="status-badge status-pending">待处理</span></td>
                                <td>2024-06-15 08:20</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <button class="btn btn-primary" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">➕</div>
                    <div>添加产品</div>
                </button>
                <button class="btn btn-outline" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">📦</div>
                    <div>订单管理</div>
                </button>
                <button class="btn btn-outline" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">👥</div>
                    <div>客户管理</div>
                </button>
                <button class="btn btn-outline" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">📊</div>
                    <div>数据分析</div>
                </button>
            </div>
        </div>
    </div>

    <!-- About Page -->
    <div id="about" class="page-section">
        <div class="page-container">
            <div class="section-header">
                <h2 class="section-title">关于友文印刷</h2>
                <p class="section-subtitle">专业印刷解决方案领导者</p>
            </div>

            <div style="background: white; padding: 4rem; border-radius: 1rem; border: 1px solid var(--border-light); text-align: center; max-width: 800px; margin: 0 auto;">
                <div style="width: 5rem; height: 5rem; background: var(--primary); border-radius: 1rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 2rem; color: white; font-size: 2rem;">🏭</div>
                <h3 style="font-size: 2rem; margin-bottom: 1.5rem; color: var(--primary); font-weight: 600;">精工印刷，品质至上</h3>
                <p style="font-size: 1.125rem; color: var(--text-secondary); line-height: 1.7; margin-bottom: 2rem;">
                    友文印刷是一家拥有15年行业经验的专业印刷服务提供商。我们专注于为各行各业的企业客户
                    提供高品质的印刷服务，从商务名片、企业文具到复杂的包装解决方案，全方位满足客户需求。
                </p>
                <p style="font-size: 1.125rem; color: var(--text-secondary); line-height: 1.7; margin-bottom: 2.5rem;">
                    我们始终坚持卓越品质、先进技术和以客户为中心的服务理念，赢得了超过500家企业客户的信赖，
                    客户满意度始终保持在99.8%的高水平。
                </p>
                <div style="margin-top: 2rem;">
                    <a href="#" onclick="showPage('products')" class="btn btn-primary btn-lg">探索我们的解决方案</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-section');
            pages.forEach(page => page.classList.remove('active'));

            // Show target page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // Update navigation
            const navLinks = document.querySelectorAll('.nav-links a');
            navLinks.forEach(link => link.classList.remove('active'));

            const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }

            // Smooth scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Enhanced interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add subtle animations to cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all cards
            document.querySelectorAll('.product-card, .stat-card, .visual-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
