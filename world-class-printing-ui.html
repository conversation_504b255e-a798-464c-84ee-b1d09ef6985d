<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PRINTEX | Enterprise Printing Solutions</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #0F172A;
            --primary-light: #1E293B;
            --secondary: #64748B;
            --accent: #3B82F6;
            --surface: #FFFFFF;
            --surface-alt: #F8FAFC;
            --surface-hover: #F1F5F9;
            --border: #E2E8F0;
            --border-light: #F1F5F9;
            --text-primary: #0F172A;
            --text-secondary: #475569;
            --text-muted: #64748B;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.5;
            color: var(--text-primary);
            background: var(--surface);
            font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-light);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow-sm);
        }

        .nav-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 72px;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.2s ease;
            cursor: pointer;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary);
        }

        .nav-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.625rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
        }

        .btn-ghost {
            color: var(--text-secondary);
            background: transparent;
        }

        .btn-ghost:hover {
            color: var(--primary);
            background: var(--surface-hover);
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Hero Section */
        .hero {
            padding: 9rem 0 6rem;
            background: linear-gradient(135deg, #FAFBFC 0%, #F1F5F9 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 60%;
            height: 100%;
            background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
        }

        .hero-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-content h1 {
            font-size: 3.75rem;
            font-weight: 700;
            line-height: 1.1;
            letter-spacing: -0.025em;
            color: var(--primary);
            margin-bottom: 1.5rem;
        }

        .hero-content .subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .hero-metrics {
            display: flex;
            gap: 2rem;
            margin-bottom: 2.5rem;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            line-height: 1;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-lg {
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
        }

        .btn-outline {
            background: white;
            color: var(--primary);
            border: 1px solid var(--border);
        }

        .btn-outline:hover {
            background: var(--surface-hover);
            border-color: var(--primary);
        }

        /* Hero Visual */
        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .visual-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            width: 100%;
            max-width: 400px;
        }

        .visual-card {
            background: white;
            padding: 2rem 1.5rem;
            border-radius: 1rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-light);
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .visual-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .visual-card:nth-child(1) { transform: translateY(-0.5rem); }
        .visual-card:nth-child(2) { transform: translateY(0.5rem); }
        .visual-card:nth-child(3) { transform: translateY(0.5rem); }
        .visual-card:nth-child(4) { transform: translateY(-0.5rem); }

        .visual-icon {
            width: 3rem;
            height: 3rem;
            background: var(--surface-alt);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.25rem;
        }

        .visual-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Page Container */
        .page-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 6rem 2rem 2rem;
        }

        .page-section {
            display: none;
        }

        .page-section.active {
            display: block;
        }

        /* Section Headers */
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 1rem;
            letter-spacing: -0.025em;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Products Grid */
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 1rem;
            border: 1px solid var(--border-light);
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
            border-color: var(--border);
        }

        .product-header {
            padding: 2rem;
            background: var(--surface-alt);
            text-align: center;
            border-bottom: 1px solid var(--border-light);
        }

        .product-icon {
            width: 4rem;
            height: 4rem;
            background: var(--primary);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .product-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .product-price {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .product-body {
            padding: 2rem;
        }

        .product-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .product-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .product-features li {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .product-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #10B981;
            font-weight: 600;
        }

        /* Admin Dashboard */
        .admin-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            padding: 3rem 2rem;
            border-radius: 1rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .admin-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            letter-spacing: -0.025em;
        }

        .admin-subtitle {
            opacity: 0.8;
            font-size: 1.125rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            border: 1px solid var(--border-light);
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            background: var(--surface-alt);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.25rem;
            color: var(--text-muted);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* Table */
        .table-container {
            background: white;
            border-radius: 1rem;
            border: 1px solid var(--border-light);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-light);
        }

        .table th {
            background: var(--surface-alt);
            font-weight: 600;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .table td {
            font-size: 0.875rem;
        }

        .table tbody tr:hover {
            background: var(--surface-alt);
        }

        /* Status Badges */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            background: #FEF3C7;
            color: #92400E;
        }

        .status-completed {
            background: #D1FAE5;
            color: #065F46;
        }

        .status-cancelled {
            background: #FEE2E2;
            color: #991B1B;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-container {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .visual-grid {
                max-width: 300px;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="#" class="logo">PRINTEX</a>
            <ul class="nav-links">
                <li><a href="#" onclick="showPage('home')" class="active">Home</a></li>
                <li><a href="#" onclick="showPage('products')">Products</a></li>
                <li><a href="#" onclick="showPage('admin')">Dashboard</a></li>
                <li><a href="#" onclick="showPage('about')">About</a></li>
            </ul>
            <div class="nav-actions">
                <a href="#" class="btn btn-ghost">Sign In</a>
                <a href="#" class="btn btn-primary">Get Quote</a>
            </div>
        </div>
    </nav>

    <!-- Home Page -->
    <div id="home" class="page-section active">
        <section class="hero">
            <div class="hero-container">
                <div class="hero-content">
                    <h1>Enterprise Printing Solutions</h1>
                    <p class="subtitle">Transform your business communications with premium printing services. From business cards to packaging solutions, we deliver excellence at scale.</p>

                    <div class="hero-metrics">
                        <div class="metric">
                            <span class="metric-value">500+</span>
                            <span class="metric-label">Enterprise Clients</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">15</span>
                            <span class="metric-label">Years Experience</span>
                        </div>
                        <div class="metric">
                            <span class="metric-value">99.8%</span>
                            <span class="metric-label">Satisfaction Rate</span>
                        </div>
                    </div>

                    <div class="hero-actions">
                        <a href="#" onclick="showPage('products')" class="btn btn-primary btn-lg">Explore Products</a>
                        <a href="#" onclick="showPage('admin')" class="btn btn-outline btn-lg">Dashboard</a>
                    </div>
                </div>

                <div class="hero-visual">
                    <div class="visual-grid">
                        <div class="visual-card">
                            <div class="visual-icon">📄</div>
                            <div class="visual-title">Business Cards</div>
                        </div>
                        <div class="visual-card">
                            <div class="visual-icon">📋</div>
                            <div class="visual-title">Forms & Invoices</div>
                        </div>
                        <div class="visual-card">
                            <div class="visual-icon">📦</div>
                            <div class="visual-title">Packaging</div>
                        </div>
                        <div class="visual-card">
                            <div class="visual-icon">📖</div>
                            <div class="visual-title">Brochures</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Products Page -->
    <div id="products" class="page-section">
        <div class="page-container">
            <div class="section-header">
                <h2 class="section-title">Premium Products</h2>
                <p class="section-subtitle">Professional printing solutions designed for modern businesses</p>
            </div>

            <div class="products-grid">
                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📄</div>
                        <div class="product-title">Business Cards</div>
                        <div class="product-price">From ¥0.15 per card</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">Premium business cards that make lasting impressions. Multiple paper stocks and finishing options available.</div>
                        <ul class="product-features">
                            <li>Standard 90×54mm format</li>
                            <li>300gsm premium cardstock</li>
                            <li>Double-sided printing</li>
                            <li>Lamination & foil options</li>
                            <li>Minimum order: 100 cards</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">Customize Now</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📋</div>
                        <div class="product-title">Carbonless Forms</div>
                        <div class="product-price">From ¥0.08 per set</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">Professional carbonless forms for invoices, receipts, and business documentation.</div>
                        <ul class="product-features">
                            <li>A4/A5 standard sizes</li>
                            <li>NCR carbonless paper</li>
                            <li>2-part or 3-part sets</li>
                            <li>Sequential numbering</li>
                            <li>Minimum order: 500 sets</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">Customize Now</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📦</div>
                        <div class="product-title">Custom Packaging</div>
                        <div class="product-price">From ¥2.50 per box</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">Custom packaging solutions that protect your products and enhance your brand.</div>
                        <ul class="product-features">
                            <li>3-ply or 5-ply corrugated</li>
                            <li>Custom dimensions</li>
                            <li>Full-color printing</li>
                            <li>Structural design support</li>
                            <li>Minimum order: 50 boxes</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">Customize Now</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📖</div>
                        <div class="product-title">Corporate Brochures</div>
                        <div class="product-price">From ¥8.50 per piece</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">High-quality brochures and catalogs that showcase your business professionally.</div>
                        <ul class="product-features">
                            <li>A4/A5 multiple formats</li>
                            <li>157gsm art paper</li>
                            <li>Saddle-stitch binding</li>
                            <li>Design consultation</li>
                            <li>Minimum order: 20 pieces</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">Customize Now</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">🏷️</div>
                        <div class="product-title">Labels & Stickers</div>
                        <div class="product-price">From ¥0.05 per label</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">Durable labels and stickers for product identification and branding.</div>
                        <ul class="product-features">
                            <li>Vinyl/PET materials</li>
                            <li>Waterproof & oil-resistant</li>
                            <li>Strong adhesive backing</li>
                            <li>Die-cut to shape</li>
                            <li>Minimum order: 1,000 labels</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">Customize Now</a>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-header">
                        <div class="product-icon">📄</div>
                        <div class="product-title">Marketing Flyers</div>
                        <div class="product-price">From ¥0.12 per flyer</div>
                    </div>
                    <div class="product-body">
                        <div class="product-description">Eye-catching flyers and promotional materials to boost your marketing efforts.</div>
                        <ul class="product-features">
                            <li>A4/A5/DL formats</li>
                            <li>128gsm art paper</li>
                            <li>Full-color printing</li>
                            <li>Gloss/matt lamination</li>
                            <li>Minimum order: 500 flyers</li>
                        </ul>
                        <a href="#" class="btn btn-primary" style="width: 100%;">Customize Now</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="admin" class="page-section">
        <div class="page-container">
            <div class="admin-header">
                <h2 class="admin-title">Management Dashboard</h2>
                <p class="admin-subtitle">PRINTEX Business Intelligence Center</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-number">156</div>
                    <div class="stat-label">Total Products</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📋</div>
                    <div class="stat-number">23</div>
                    <div class="stat-label">Today's Orders</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-number">8</div>
                    <div class="stat-label">Pending Orders</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">Total Clients</div>
                </div>
            </div>

            <div style="margin-bottom: 3rem;">
                <h3 style="margin-bottom: 1.5rem; font-size: 1.5rem; font-weight: 600; color: var(--primary);">Recent Orders</h3>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Client</th>
                                <th>Product</th>
                                <th>Specifications</th>
                                <th>Quantity</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#PX240615001</td>
                                <td>Shenzhen Tech Ltd.</td>
                                <td>Business Cards</td>
                                <td>90×54mm/Double-sided/Laminated</td>
                                <td>1,000 pcs</td>
                                <td>¥230.00</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>2024-06-15 10:30</td>
                            </tr>
                            <tr>
                                <td>#PX240615002</td>
                                <td>Guangzhou Trading Co.</td>
                                <td>Carbonless Forms</td>
                                <td>A4/3-part NCR</td>
                                <td>500 sets</td>
                                <td>¥120.00</td>
                                <td><span class="status-badge status-completed">Completed</span></td>
                                <td>2024-06-15 09:15</td>
                            </tr>
                            <tr>
                                <td>#PX240615003</td>
                                <td>Dongguan Manufacturing</td>
                                <td>Custom Packaging</td>
                                <td>30×20×15cm/5-ply</td>
                                <td>100 boxes</td>
                                <td>¥350.00</td>
                                <td><span class="status-badge status-cancelled">Cancelled</span></td>
                                <td>2024-06-15 08:45</td>
                            </tr>
                            <tr>
                                <td>#PX240615004</td>
                                <td>Foshan Electronics</td>
                                <td>Corporate Brochures</td>
                                <td>A4/20 pages/Perfect bound</td>
                                <td>50 pieces</td>
                                <td>¥425.00</td>
                                <td><span class="status-badge status-pending">Pending</span></td>
                                <td>2024-06-15 08:20</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <button class="btn btn-primary" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">➕</div>
                    <div>Add Product</div>
                </button>
                <button class="btn btn-outline" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">📦</div>
                    <div>Order Management</div>
                </button>
                <button class="btn btn-outline" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">👥</div>
                    <div>Client Management</div>
                </button>
                <button class="btn btn-outline" style="padding: 1.5rem; flex-direction: column; height: auto; gap: 0.75rem;">
                    <div style="font-size: 1.5rem;">📊</div>
                    <div>Analytics</div>
                </button>
            </div>
        </div>
    </div>

    <!-- About Page -->
    <div id="about" class="page-section">
        <div class="page-container">
            <div class="section-header">
                <h2 class="section-title">About PRINTEX</h2>
                <p class="section-subtitle">Leading enterprise printing solutions provider</p>
            </div>

            <div style="background: white; padding: 4rem; border-radius: 1rem; border: 1px solid var(--border-light); text-align: center; max-width: 800px; margin: 0 auto;">
                <div style="width: 5rem; height: 5rem; background: var(--primary); border-radius: 1rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 2rem; color: white; font-size: 2rem;">🏭</div>
                <h3 style="font-size: 2rem; margin-bottom: 1.5rem; color: var(--primary); font-weight: 600;">Excellence in Every Print</h3>
                <p style="font-size: 1.125rem; color: var(--text-secondary); line-height: 1.7; margin-bottom: 2rem;">
                    PRINTEX is a premier enterprise printing solutions provider with over 15 years of industry expertise.
                    We specialize in delivering high-quality printing services to businesses across various industries,
                    from business cards and corporate stationery to complex packaging solutions.
                </p>
                <p style="font-size: 1.125rem; color: var(--text-secondary); line-height: 1.7; margin-bottom: 2.5rem;">
                    Our commitment to excellence, cutting-edge technology, and customer-centric approach has earned us
                    the trust of over 500 enterprise clients, maintaining a 99.8% satisfaction rate throughout our journey.
                </p>
                <div style="margin-top: 2rem;">
                    <a href="#" onclick="showPage('products')" class="btn btn-primary btn-lg">Explore Our Solutions</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-section');
            pages.forEach(page => page.classList.remove('active'));

            // Show target page
            const targetPage = document.getElementById(pageId);
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // Update navigation
            const navLinks = document.querySelectorAll('.nav-links a');
            navLinks.forEach(link => link.classList.remove('active'));

            const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }

            // Smooth scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Enhanced interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add subtle animations to cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all cards
            document.querySelectorAll('.product-card, .stat-card, .visual-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
